#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando Filtro de Kalman
Substitui a média móvel de 50 dias pela previsão do filtro de Kalman
Mantém a média móvel de 200 dias e faz previsão 20 dias à frente
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
from kalman import KalmanFilter
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def configurar_filtro_kalman_com_volume(precos, volumes):
    """
    Configura o filtro de Kalman para previsão de preços de ações usando volume
    Modelo: preço[t+1] = preço[t] + tendência[t] + volume_effect[t] + ruído
    Estado: [preço, tendência, volume_effect]
    """
    # Normalizar volumes para evitar problemas numéricos
    volume_normalizado = (volumes - volumes.mean()) / volumes.std()

    # Matrizes do modelo de espaço de estados (3D: preço, tendência, efeito_volume)
    F = np.array([[1, 1],    # preço[t+1] = preço[t] + tendência[t] + efeito_volume[t]
                  [0, 1]])   # tendência[t+1] = tendência[t]+volume[t])

    # Matriz de controle - volume influencia o efeito do volume
    B = np.array([[0],          # Volume não afeta diretamente o preço
                  [0.1]])       # Volume afeta o efeito do volume

    H = np.array([[1, 0]])   # Observamos apenas o preço

    # Covariâncias (ajustadas para modelo com volume)
    Q = np.array([[0.1, 0],      # Ruído no processo (preço)
                  [0, 0.01]])     # Ruído no processo (tendência)
                  

    R = np.array([[1.0]])           # Ruído na observação

    # Estado inicial
    x0 = np.array([[precos[0]],     # Preço inicial
                   [0]])             # Tendência inicial zero
                   

    P0 = np.array([[1, 0],       # Covariância inicial
                   [0, 1]])
                   

    kf = KalmanFilter(F, B, H, Q, R, x0, P0)

    return kf, volume_normalizado

def aplicar_filtro_kalman_com_volume(precos, volumes):
    """
    Aplica o filtro de Kalman aos preços usando volume como entrada de controle
    """
    try:
        # Configurar filtro com volume
        kf, volume_normalizado = configurar_filtro_kalman_com_volume(precos, volumes)

        # Aplicar filtro sequencialmente
        precos_filtrados = []
        estados = []
        covariancias = []

        for i, preco in enumerate(precos):
            # Entrada de controle baseada no volume normalizado
            u = np.array([[volume_normalizado.iloc[i]]])

            # Predição
            kf.predict(u)

            # Atualização com observação
            z = np.array([[preco]])
            estado_atualizado = kf.update(z)

            # Armazenar resultados
            precos_filtrados.append(estado_atualizado[0, 0])  # Preço filtrado
            estados.append(estado_atualizado.copy())
            covariancias.append(kf.P.copy())

        precos_filtrados = np.array(precos_filtrados)

        return precos_filtrados, kf, estados, covariancias, volume_normalizado

    except Exception as e:
        print(f"     ⚠️ Erro no filtro de Kalman com volume: {e}")
        return None, None, None, None, None

def prever_kalman_com_volume(kf, ultimo_estado, ultima_covariancia, volume_medio_normalizado, dias_previsao=20):
    """
    Faz previsão usando o filtro de Kalman com volume médio como entrada de controle
    """
    try:
        previsoes = []

        # Criar uma cópia do filtro para não alterar o original
        kf_pred = KalmanFilter(kf.F, kf.B, kf.H, kf.Q, kf.R, ultimo_estado.copy(), ultima_covariancia.copy())

        # Entrada de controle baseada no volume médio normalizado
        u = np.array([[volume_medio_normalizado]])

        for _ in range(dias_previsao):
            # Predição do próximo estado
            estado_pred = kf_pred.predict(u)

            # Previsão do preço (primeira componente do estado)
            preco_previsto = estado_pred[0, 0]
            previsoes.append(preco_previsto)

            # Para próxima iteração, usar o estado predito como base
            # (sem atualização, pois não temos observações futuras)

        return np.array(previsoes)

    except Exception as e:
        print(f"     ⚠️ Erro na previsão Kalman com volume: {e}")
        return None

def obter_dados_com_kalman_volume(ticker, nome):
    """
    Obtém dados de 15 meses e aplica filtro de Kalman com volume
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)
        # Obter 15 meses para ter dados suficientes para MM200
        dados = stock.history(period="15mo")
        print(dados.columns)
        if dados.empty or len(dados) < 200:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        # Calcular média móvel de 200 dias
        dados['MM200'] = dados['Close'].rolling(window=200).mean()

        # Aplicar filtro de Kalman com volume
        resultado_kalman = aplicar_filtro_kalman_com_volume(dados['Close'], dados['Volume'])

        if resultado_kalman[0] is None:
            return None

        precos_filtrados, kf, estados, covariancias, volume_normalizado = resultado_kalman
        dados['Kalman'] = precos_filtrados

        # Fazer previsão 20 dias à frente usando volume médio
        ultimo_estado = estados[-1]
        ultima_cov = covariancias[-1]
        volume_medio_normalizado = volume_normalizado.mean()
        previsoes = prever_kalman_com_volume(kf, ultimo_estado, ultima_cov, volume_medio_normalizado, 20)

        # Pegar apenas os últimos 12 meses para exibição
        dados_12m = dados.tail(252)  # ~252 dias úteis em 12 meses

        print(f"     ✅ {len(dados_12m)} dias (com Kalman+Volume e MM200)")

        return dados_12m, previsoes, kf

    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia_kalman(dados):
    """
    Analisa a tendência baseada no filtro de Kalman e MM200
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Close'].iloc[-1]
    kalman_atual = dados['Kalman'].iloc[-1]
    mm200_atual = dados['MM200'].iloc[-1]

    # Verificar se os valores estão válidos
    if pd.isna(kalman_atual) or pd.isna(mm200_atual):
        return "Indefinida", "gray"

    # Análise de tendência baseada em Kalman
    if preco_atual > kalman_atual > mm200_atual:
        return "Altista Forte", "darkgreen"
    elif preco_atual > kalman_atual and kalman_atual < mm200_atual:
        return "Altista Moderada", "green"
    elif preco_atual < kalman_atual < mm200_atual:
        return "Baixista Forte", "darkred"
    elif preco_atual < kalman_atual and kalman_atual > mm200_atual:
        return "Baixista Moderada", "red"
    else:
        return "Lateral", "orange"

def criar_grafico_kalman(ticker, nome, dados, previsoes):
    """
    Cria gráfico com preço, filtro de Kalman, MM200 e previsões
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100

    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_kalman(dados)

    # Gráfico 1: Preço + Kalman + MM200 + Previsões
    ax1.plot(dados.index, dados['Close'], linewidth=2.5, color='#1f77b4',
             label='Preço de Fechamento', zorder=3)

    # Filtro de Kalman com Volume (substitui MM50)
    ax1.plot(dados.index, dados['Kalman'], linewidth=2, color='orange',
             label='Filtro de Kalman + Volume', alpha=0.8, zorder=2)

    # Média móvel de 200 dias
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red',
             label='MM 200 dias', alpha=0.8, zorder=2)

    # Área entre preço e Kalman
    ax1.fill_between(dados.index, dados['Close'], dados['Kalman'],
                     where=(dados['Close'] >= dados['Kalman']),
                     color='lightgreen', alpha=0.3, interpolate=True,
                     label='Preço > Kalman')
    ax1.fill_between(dados.index, dados['Close'], dados['Kalman'],
                     where=(dados['Close'] < dados['Kalman']),
                     color='lightcoral', alpha=0.3, interpolate=True,
                     label='Preço < Kalman')

    # Adicionar previsões se disponíveis
    if previsoes is not None and len(previsoes) > 0:
        # Criar datas futuras para as previsões
        ultima_data = dados.index[-1]
        datas_futuras = pd.date_range(start=ultima_data + timedelta(days=1),
                                     periods=len(previsoes), freq='D')

        # Plotar previsões
        ax1.plot(datas_futuras, previsoes, linewidth=2, color='purple',
                linestyle='--', label='Previsão Kalman (20 dias)', alpha=0.8, zorder=2)

        # Conectar último ponto real com primeira previsão
        ax1.plot([ultima_data, datas_futuras[0]],
                [dados['Close'].iloc[-1], previsoes[0]],
                linewidth=1, color='purple', linestyle=':', alpha=0.6)

    ax1.set_title(f'{nome} ({ticker.replace(".SA", "")}) - Filtro de Kalman + Volume (12 Meses)',
                  fontsize=16, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Estatísticas no gráfico
    kalman_atual = dados['Kalman'].iloc[-1] if not pd.isna(dados['Kalman'].iloc[-1]) else 0
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Preço: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'Kalman: R$ {kalman_atual:.2f} | MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'

    if previsoes is not None and len(previsoes) > 0:
        previsao_final = previsoes[-1]
        variacao_previsao = ((previsao_final / preco_final) - 1) * 100
        stats_text += f'\nPrevisão 20d: R$ {previsao_final:.2f} ({variacao_previsao:+.1f}%)'

    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9,
                      edgecolor=cor_tendencia, linewidth=2))

    # Gráfico 2: Volume
    ax2.bar(dados.index, dados['Volume']/1e6, alpha=0.7, color='purple', width=0.8)
    ax2.set_title('Volume de Negociação', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Volume (Milhões)', fontsize=12)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/kalman_analysis', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"results/figures/kalman_analysis/kalman_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")

    resultado = {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,
        'kalman': kalman_atual,
        'mm200': mm200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia
    }

    if previsoes is not None and len(previsoes) > 0:
        resultado['previsao_20d'] = previsoes[-1]
        resultado['variacao_previsao'] = ((previsoes[-1] / preco_final) - 1) * 100

    return resultado

def criar_dashboard_kalman(resultados):
    """
    Cria dashboard com análise do filtro de Kalman
    """
    print("\n📊 Criando dashboard de análise Kalman...")

    # Separar por tendência
    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r)

    # Criar gráfico de dashboard
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # Gráfico 1: Distribuição de Tendências
    labels = list(tendencias.keys())
    sizes = [len(tendencias[label]) for label in labels]
    colors = ['darkgreen', 'green', 'orange', 'red', 'darkred', 'gray'][:len(labels)]

    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Distribuição de Tendências (Kalman)', fontsize=14, fontweight='bold')

    # Gráfico 2: Performance vs Posição Kalman
    performances = [r['performance'] for r in resultados]
    posicoes_kalman = []
    cores_pontos = []

    for r in resultados:
        if r['preco_atual'] > r['kalman']:
            posicoes_kalman.append(1)  # Acima do Kalman
            cores_pontos.append('green')
        else:
            posicoes_kalman.append(0)  # Abaixo do Kalman
            cores_pontos.append('red')

    ax2.scatter(posicoes_kalman, performances, c=cores_pontos, alpha=0.7, s=100)
    ax2.set_xlabel('Posição relativa ao Kalman (0=Abaixo, 1=Acima)')
    ax2.set_ylabel('Performance (%)')
    ax2.set_title('Performance vs Posição Kalman', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # Gráfico 3: Ranking de Performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    tickers = [r['ticker'].replace('.SA', '') for r in resultados_ord]
    performances_top = [r['performance'] for r in resultados_ord]
    cores_bars = [r['cor_tendencia'] for r in resultados_ord]

    bars = ax3.bar(range(len(tickers)), performances_top, color=cores_bars, alpha=0.7)
    ax3.set_title('Performance das 20 Ações Diversificadas', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Ações')
    ax3.set_ylabel('Performance (%)')
    ax3.set_xticks(range(len(tickers)))
    ax3.set_xticklabels(tickers, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.8)

    # Gráfico 4: Previsões vs Performance Atual
    previsoes_var = []
    nomes_acoes = []

    for r in resultados:
        if 'variacao_previsao' in r:
            previsoes_var.append(r['variacao_previsao'])
            nomes_acoes.append(r['ticker'].replace('.SA', ''))

    if previsoes_var:
        cores_prev = ['green' if p > 0 else 'red' for p in previsoes_var]

        ax4.bar(range(len(nomes_acoes)), previsoes_var, color=cores_prev, alpha=0.7)
        ax4.set_title('Previsões Kalman - 20 Dias', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Ações')
        ax4.set_ylabel('Variação Prevista (%)')
        ax4.set_xticks(range(len(nomes_acoes)))
        ax4.set_xticklabels(nomes_acoes, rotation=45, ha='right')
        ax4.grid(True, alpha=0.3, axis='y')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    else:
        ax4.text(0.5, 0.5, 'Previsões não disponíveis',
                transform=ax4.transAxes, ha='center', va='center')
        ax4.set_title('Previsões Kalman - 20 Dias', fontsize=14, fontweight='bold')

    plt.suptitle('Dashboard - Análise com Filtro de Kalman', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/dashboards', exist_ok=True)

    plt.savefig('results/figures/dashboards/dashboard_kalman.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/dashboards/dashboard_kalman.png")

def gerar_relatorio_kalman(resultados):
    """
    Gera relatório detalhado da análise com Kalman
    """
    print("\n" + "="*110)
    print("📈 RELATÓRIO ANÁLISE TÉCNICA - FILTRO DE KALMAN + MM200")
    print("="*110)

    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Preço':<8} {'Kalman':<8} {'MM200':<8} {'Prev20d':<8} {'Tendência':<15}")
    print("-" * 110)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"
        prev_text = f"{r.get('previsao_20d', 0):.2f}" if 'previsao_20d' in r else "N/A"
        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>6.2f} {r['kalman']:>6.2f} {r['mm200']:>6.2f} "
              f"{prev_text:>6s} {r['tendencia']:<15}")

    # Estatísticas por tendência
    print("\n" + "="*110)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*110)

    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])

    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")

    # Análise de posicionamento
    print("\n" + "="*110)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*110)

    acima_kalman = len([r for r in resultados if r['preco_atual'] > r['kalman']])
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])
    kalman_acima_mm200 = len([r for r in resultados if r['kalman'] > r['mm200']])

    print(f"Ações com preço acima do Kalman:  {acima_kalman:2d}/{len(resultados)} ({acima_kalman/len(resultados)*100:.1f}%)")
    print(f"Ações com preço acima da MM200:  {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")
    print(f"Ações com Kalman > MM200:       {kalman_acima_mm200:2d}/{len(resultados)} ({kalman_acima_mm200/len(resultados)*100:.1f}%)")

    # Análise de previsões
    previsoes_positivas = len([r for r in resultados if r.get('variacao_previsao', 0) > 0])
    if previsoes_positivas > 0:
        print(f"\n🔮 ANÁLISE DE PREVISÕES (20 DIAS)")
        print("="*110)
        print(f"Previsões positivas: {previsoes_positivas:2d}/{len(resultados)} ({previsoes_positivas/len(resultados)*100:.1f}%)")

        # Top 5 previsões mais otimistas
        with_predictions = [r for r in resultados if 'variacao_previsao' in r]
        top_predictions = sorted(with_predictions, key=lambda x: x['variacao_previsao'], reverse=True)[:5]

        print("\nTOP 5 Previsões Mais Otimistas:")
        for i, r in enumerate(top_predictions, 1):
            print(f"{i}. {r['ticker'].replace('.SA', ''):<6} - {r['nome'][:20]:<20} | "
                  f"Previsão: {r['variacao_previsao']:+6.1f}%")

def main():
    print("🚀 ANÁLISE COM FILTRO DE KALMAN + VOLUME - AÇÕES DIVERSIFICADAS")
    print("="*75)
    print("📊 Análise Técnica com:")
    print("   • Preço de fechamento")
    print("   • Filtro de Kalman com Volume (substitui MM50)")
    print("   • Média Móvel de 200 dias (MM200)")
    print("   • Volume como entrada de controle")
    print("   • Previsão 20 dias à frente")
    print("   • Análise de tendência")
    print("   • Volume de negociação")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")
    print("⏱️  Tempo estimado: 5-8 minutos (processamento Kalman + Volume)")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    print(f"\n📈 Iniciando análise com filtro de Kalman + Volume...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        resultado_dados = obter_dados_com_kalman_volume(ticker, nome)

        if resultado_dados is not None:
            if len(resultado_dados) == 3:
                dados, previsoes, kf = resultado_dados
                resultado = criar_grafico_kalman(ticker, nome, dados, previsoes)
                resultados.append(resultado)
                sucesso += 1
            else:
                erro += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if resultados:
        # Criar dashboard
        criar_dashboard_kalman(resultados)

        # Gerar relatório
        gerar_relatorio_kalman(resultados)

        print(f"\n📁 ARQUIVOS GERADOS:")
        print(f"   • {sucesso} gráficos individuais com análise Kalman + Volume")
        print("   • 1 dashboard de análise técnica")
        print("\n🎯 Todos os gráficos incluem:")
        print("   • Linha do preço de fechamento")
        print("   • Linha do filtro de Kalman com Volume (laranja)")
        print("   • Linha da MM200 (vermelha)")
        print("   • Área colorida (verde=preço>Kalman, vermelho=preço<Kalman)")
        print("   • Previsão 20 dias à frente com volume médio (linha pontilhada roxa)")
        print("   • Análise de tendência automática")
        print("   • Volume usado como entrada de controle no filtro")

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame(resultados)
            os.makedirs('results/csv/kalman_analysis', exist_ok=True)
            csv_path = 'results/csv/kalman_analysis/resultados_kalman.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados salvos em: {csv_path}")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
