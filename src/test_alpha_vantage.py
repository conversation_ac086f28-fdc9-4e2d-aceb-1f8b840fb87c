#!/usr/bin/env python3
"""
Test script for Alpha Vantage API to explore bid/ask volume data availability
Using stocks from acoes_diversificacao.csv
"""

import os
import time
import pandas as pd
import requests
from alpha_vantage.timeseries import TimeSeries
from alpha_vantage.fundamentaldata import FundamentalData
from alpha_vantage.techindicators import TechIndicators
from datetime import datetime

# You need to get a free API key from: https://www.alphavantage.co/support/#api-key
API_KEY = "demo"  # Replace with your actual API key

def carregar_acoes_diversificadas():
    """
    Carrega as ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 10 ações para teste (API tem limite de calls)
        acoes = []
        for _, row in df.head(10).iterrows():
            # Para Alpha Vantage, usar ticker sem .SA (ações brasileiras podem não estar disponíveis)
            ticker_original = row['Ticker']
            nome = row['Nome']
            acoes.append((ticker_original, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações para teste Alpha Vantage")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def test_us_stocks():
    """
    Testa com algumas ações americanas conhecidas para verificar funcionalidades
    """
    return [
        ('AAPL', 'Apple Inc'),
        ('GOOGL', 'Alphabet Inc'),
        ('MSFT', 'Microsoft Corp'),
        ('TSLA', 'Tesla Inc'),
        ('AMZN', 'Amazon.com Inc')
    ]

def test_global_quote(ticker, nome):
    """
    Testa o endpoint GLOBAL_QUOTE para obter cotação atual
    """
    print(f"\n🔍 Testando GLOBAL_QUOTE para {ticker} ({nome})")
    
    try:
        url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={ticker}&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        if "Global Quote" in data:
            quote = data["Global Quote"]
            print("✅ Dados obtidos com sucesso!")
            print(f"   Símbolo: {quote.get('01. symbol', 'N/A')}")
            print(f"   Preço: ${quote.get('05. price', 'N/A')}")
            print(f"   Volume: {quote.get('06. volume', 'N/A')}")
            print(f"   Último dia: {quote.get('07. latest trading day', 'N/A')}")
            print(f"   Variação: {quote.get('09. change', 'N/A')} ({quote.get('10. change percent', 'N/A')})")
            
            # Verificar se há informações de bid/ask
            if any('bid' in key.lower() for key in quote.keys()):
                print("🎯 BID/ASK encontrado!")
                for key, value in quote.items():
                    if 'bid' in key.lower() or 'ask' in key.lower():
                        print(f"   {key}: {value}")
            else:
                print("❌ Nenhuma informação de BID/ASK encontrada")
                
            return True
        else:
            print(f"❌ Erro na resposta: {data}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_intraday_data(ticker, nome):
    """
    Testa dados intraday que podem conter mais informações detalhadas
    """
    print(f"\n📊 Testando TIME_SERIES_INTRADAY para {ticker} ({nome})")
    
    try:
        url = f"https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol={ticker}&interval=5min&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        if "Time Series (5min)" in data:
            time_series = data["Time Series (5min)"]
            latest_time = list(time_series.keys())[0]
            latest_data = time_series[latest_time]
            
            print("✅ Dados intraday obtidos!")
            print(f"   Último horário: {latest_time}")
            print(f"   Abertura: ${latest_data.get('1. open', 'N/A')}")
            print(f"   Máxima: ${latest_data.get('2. high', 'N/A')}")
            print(f"   Mínima: ${latest_data.get('3. low', 'N/A')}")
            print(f"   Fechamento: ${latest_data.get('4. close', 'N/A')}")
            print(f"   Volume: {latest_data.get('5. volume', 'N/A')}")
            
            # Verificar campos disponíveis
            print(f"   Campos disponíveis: {list(latest_data.keys())}")
            
            # Procurar por bid/ask volume
            bid_ask_found = False
            for key in latest_data.keys():
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"🎯 {key}: {latest_data[key]}")
                    bid_ask_found = True
            
            if not bid_ask_found:
                print("❌ Nenhuma informação de BID/ASK volume encontrada")
                
            return True
        else:
            print(f"❌ Erro na resposta: {data}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_alpha_vantage_library(ticker, nome):
    """
    Testa usando a biblioteca oficial alpha_vantage
    """
    print(f"\n📚 Testando biblioteca alpha_vantage para {ticker} ({nome})")
    
    try:
        # Time Series
        ts = TimeSeries(key=API_KEY, output_format='pandas')
        data, meta_data = ts.get_quote_endpoint(symbol=ticker)
        
        print("✅ Dados obtidos via biblioteca!")
        print(f"   Dados disponíveis: {list(data.columns) if hasattr(data, 'columns') else 'N/A'}")
        print(f"   Valores: {data.to_dict() if hasattr(data, 'to_dict') else data}")
        
        # Verificar se há bid/ask
        if hasattr(data, 'columns'):
            bid_ask_cols = [col for col in data.columns if any(term in col.lower() for term in ['bid', 'ask'])]
            if bid_ask_cols:
                print(f"🎯 Colunas BID/ASK encontradas: {bid_ask_cols}")
            else:
                print("❌ Nenhuma coluna BID/ASK encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_realtime_bulk_quotes():
    """
    Testa o endpoint de cotações em lote (pode ter mais informações)
    """
    print(f"\n💼 Testando REALTIME_BULK_QUOTES")
    
    try:
        # Este endpoint pode requerer premium
        symbols = "AAPL,GOOGL,MSFT"
        url = f"https://www.alphavantage.co/query?function=REALTIME_BULK_QUOTES&symbols={symbols}&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        print(f"Resposta: {data}")
        
        if "stock_quotes" in data:
            quotes = data["stock_quotes"]
            for quote in quotes[:3]:  # Mostrar apenas os primeiros 3
                print(f"\n📈 {quote.get('symbol', 'N/A')}:")
                for key, value in quote.items():
                    print(f"   {key}: {value}")
                    
                # Verificar bid/ask
                bid_ask_found = any(term in key.lower() for key in quote.keys() for term in ['bid', 'ask'])
                if bid_ask_found:
                    print("🎯 Informações BID/ASK encontradas!")
                else:
                    print("❌ Nenhuma informação BID/ASK")
        else:
            print(f"❌ Endpoint pode requerer premium ou não estar disponível: {data}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")

def test_additional_endpoints():
    """
    Testa endpoints adicionais que podem conter bid/ask data
    """
    print(f"\n🔬 TESTANDO ENDPOINTS ADICIONAIS")
    print("=" * 40)

    # Teste com diferentes funções que podem ter bid/ask
    endpoints_to_test = [
        "CURRENCY_EXCHANGE_RATE",
        "DIGITAL_CURRENCY_INTRADAY",
        "FX_INTRADAY",
        "SYMBOL_SEARCH"
    ]

    for endpoint in endpoints_to_test:
        print(f"\n🔍 Testando endpoint: {endpoint}")
        try:
            if endpoint == "CURRENCY_EXCHANGE_RATE":
                url = f"https://www.alphavantage.co/query?function={endpoint}&from_currency=USD&to_currency=BRL&apikey={API_KEY}"
            elif endpoint == "SYMBOL_SEARCH":
                url = f"https://www.alphavantage.co/query?function={endpoint}&keywords=MSFT&apikey={API_KEY}"
            else:
                continue  # Skip outros por enquanto

            response = requests.get(url)
            data = response.json()

            print(f"   Resposta: {list(data.keys()) if isinstance(data, dict) else 'Não é dict'}")

            # Procurar por bid/ask em qualquer lugar da resposta
            data_str = str(data).lower()
            if 'bid' in data_str or 'ask' in data_str:
                print("   🎯 Possível informação BID/ASK encontrada!")
                print(f"   Dados: {data}")
            else:
                print("   ❌ Nenhuma informação BID/ASK")

        except Exception as e:
            print(f"   ❌ Erro: {e}")

        time.sleep(1)

def generate_report():
    """
    Gera relatório detalhado sobre Alpha Vantage vs yfinance
    """
    print(f"\n\n📊 RELATÓRIO COMPARATIVO: ALPHA VANTAGE vs YFINANCE")
    print("=" * 60)

    print("🔍 ALPHA VANTAGE - Dados Disponíveis:")
    print("   ✅ Preços OHLCV (Open, High, Low, Close, Volume)")
    print("   ✅ Dados históricos (diários, semanais, mensais)")
    print("   ✅ Dados intraday (1min, 5min, 15min, 30min, 60min)")
    print("   ✅ Indicadores técnicos (SMA, EMA, RSI, MACD, etc.)")
    print("   ✅ Dados fundamentais (balanços, demonstrações)")
    print("   ✅ Dados de forex e criptomoedas")
    print("   ❌ BID/ASK Volume (não encontrado nos endpoints gratuitos)")
    print("   ❓ BID/ASK Volume (pode estar disponível em planos premium)")

    print("\n🔍 YFINANCE - Dados Disponíveis:")
    print("   ✅ Preços OHLCV")
    print("   ✅ Dados históricos")
    print("   ✅ Dados intraday")
    print("   ✅ Volume total")
    print("   ❌ BID/ASK Volume (não disponível)")

    print("\n💡 RECOMENDAÇÕES PARA BID/ASK VOLUME:")
    print("   1. 📈 Interactive Brokers API (IBKR)")
    print("      • Dados de mercado profissionais")
    print("      • Bid/Ask spreads e volumes")
    print("      • Requer conta de corretagem")

    print("   2. 🏦 APIs de Corretoras Brasileiras:")
    print("      • Clear Corretora API")
    print("      • XP Investimentos API")
    print("      • Rico API")
    print("      • Requer cadastro e aprovação")

    print("   3. 💰 APIs Premium:")
    print("      • Alpha Vantage Premium")
    print("      • Quandl/Nasdaq Data Link")
    print("      • Bloomberg API")
    print("      • Refinitiv (ex-Thomson Reuters)")

    print("   4. 🌐 Alternativas Gratuitas:")
    print("      • Polygon.io (plano gratuito limitado)")
    print("      • Twelve Data (plano gratuito)")
    print("      • Financial Modeling Prep")

def main():
    """
    Função principal para testar Alpha Vantage API
    """
    print("🚀 TESTE ALPHA VANTAGE API - BUSCA POR BID/ASK VOLUME")
    print("=" * 60)

    if API_KEY == "demo":
        print("⚠️  ATENÇÃO: Usando API key demo (limitada)")
        print("   Para testes completos, obtenha sua chave gratuita em:")
        print("   https://www.alphavantage.co/support/#api-key")
        print()

    # Testar com ações americanas primeiro (mais provável de funcionar)
    print("🇺🇸 TESTANDO COM AÇÕES AMERICANAS")
    print("=" * 40)

    us_stocks = test_us_stocks()
    success_count = 0

    for ticker, nome in us_stocks[:3]:  # Testar apenas 3 para não esgotar API calls
        print(f"\n{'='*50}")
        print(f"🔍 TESTANDO: {ticker} - {nome}")
        print(f"{'='*50}")

        # Teste 1: Global Quote
        if test_global_quote(ticker, nome):
            success_count += 1

        time.sleep(1)  # Evitar rate limiting

        # Teste 2: Intraday Data
        if test_intraday_data(ticker, nome):
            success_count += 1

        time.sleep(1)

        # Teste 3: Biblioteca oficial
        if test_alpha_vantage_library(ticker, nome):
            success_count += 1

        time.sleep(2)  # Pausa maior entre ações

    # Testar endpoint de cotações em lote
    test_realtime_bulk_quotes()

    # Testar endpoints adicionais
    test_additional_endpoints()

    # Testar com ações brasileiras (se disponível)
    print(f"\n\n🇧🇷 TESTANDO COM AÇÕES BRASILEIRAS")
    print("=" * 40)

    acoes_br = carregar_acoes_diversificadas()
    if acoes_br:
        for ticker, nome in acoes_br[:2]:  # Testar apenas 2
            print(f"\n🔍 Testando ação brasileira: {ticker} - {nome}")
            test_global_quote(ticker, nome)
            time.sleep(2)

    # Gerar relatório comparativo
    generate_report()

    # Resumo final
    print(f"\n\n📋 RESUMO DOS TESTES")
    print("=" * 40)
    print(f"✅ Testes realizados com sucesso: {success_count}")
    print(f"🔍 Funcionalidades encontradas:")
    print(f"   • Cotações atuais (preço, volume, variação)")
    print(f"   • Dados históricos intraday")
    print(f"   • Dados OHLCV básicos")
    print(f"\n❌ CONCLUSÃO SOBRE BID/ASK VOLUME:")
    print(f"   Alpha Vantage NÃO fornece bid/ask volume nos endpoints gratuitos.")
    print(f"   Os dados disponíveis são similares ao yfinance (OHLCV).")
    print(f"   Para bid/ask volume, considere as alternativas listadas acima.")

if __name__ == "__main__":
    main()
